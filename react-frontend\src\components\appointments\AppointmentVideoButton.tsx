import React, { useState } from 'react';
import styled from 'styled-components';
import { Video, Calendar, Clock, User, AlertCircle, CheckCircle } from 'lucide-react';
import { videoCallService, VideoConsultation, ConsultationType } from '../../services/videoCallService';
import { Appointment } from '../../types';

const VideoButtonContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const VideoButton = styled.button<{ variant?: 'primary' | 'secondary' | 'success' }>`
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  
  background: ${props => {
    switch (props.variant) {
      case 'primary': return '#4299e1';
      case 'success': return '#48bb78';
      default: return '#edf2f7';
    }
  }};
  
  color: ${props => props.variant === 'secondary' ? '#4a5568' : 'white'};
  
  &:hover {
    background: ${props => {
      switch (props.variant) {
        case 'primary': return '#3182ce';
        case 'success': return '#38a169';
        default: return '#e2e8f0';
      }
    }};
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const StatusMessage = styled.div<{ type: 'success' | 'error' | 'info' }>`
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  
  background: ${props => {
    switch (props.type) {
      case 'success': return '#c6f6d5';
      case 'error': return '#fed7d7';
      default: return '#bee3f8';
    }
  }};
  
  color: ${props => {
    switch (props.type) {
      case 'success': return '#2f855a';
      case 'error': return '#c53030';
      default: return '#2b6cb0';
    }
  }};
`;

const ConsultationInfo = styled.div`
  background: #f7fafc;
  padding: 12px;
  border-radius: 6px;
  font-size: 12px;
  color: #4a5568;
`;

const InfoRow = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

interface AppointmentVideoButtonProps {
  appointment: Appointment;
  onConsultationCreated?: (consultation: VideoConsultation) => void;
  onError?: (error: string) => void;
}

const AppointmentVideoButton: React.FC<AppointmentVideoButtonProps> = ({
  appointment,
  onConsultationCreated,
  onError
}) => {
  const [consultation, setConsultation] = useState<VideoConsultation | null>(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  const canCreateVideoConsultation = (): boolean => {
    return appointment.type === 'VIDEO_CALL' && 
           (appointment.status === 'CONFIRMED' || appointment.status === 'SCHEDULED');
  };

  const canJoinConsultation = (): boolean => {
    if (!consultation) return false;
    
    const now = new Date();
    const appointmentDateTime = new Date(`${appointment.date}T${appointment.startTime}`);
    const timeDiff = appointmentDateTime.getTime() - now.getTime();
    
    // Allow joining 15 minutes before appointment time
    return timeDiff <= 15 * 60 * 1000 && timeDiff >= -60 * 60 * 1000; // Up to 1 hour after
  };

  const getConsultationType = (): ConsultationType => {
    // Map appointment reason to consultation type
    const reason = appointment.reasonForVisit?.toLowerCase() || '';
    
    if (reason.includes('follow') || reason.includes('follow-up')) {
      return 'FOLLOW_UP';
    } else if (reason.includes('urgent') || reason.includes('emergency')) {
      return 'URGENT_CARE';
    } else if (reason.includes('mental') || reason.includes('therapy')) {
      return 'MENTAL_HEALTH';
    } else if (reason.includes('prescription') || reason.includes('medication')) {
      return 'PRESCRIPTION_REVIEW';
    } else if (reason.includes('specialist')) {
      return 'SPECIALIST_CONSULTATION';
    } else {
      return 'ROUTINE_CHECKUP';
    }
  };

  const createVideoConsultation = async () => {
    try {
      setLoading(true);
      setMessage(null);
      
      const consultationType = getConsultationType();
      const newConsultation = await videoCallService.createConsultation(
        appointment.id,
        consultationType
      );
      
      setConsultation(newConsultation);
      setMessage({
        type: 'success',
        text: 'Video consultation created successfully!'
      });
      
      if (onConsultationCreated) {
        onConsultationCreated(newConsultation);
      }
      
      // Clear message after 3 seconds
      setTimeout(() => setMessage(null), 3000);
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to create video consultation';
      setMessage({
        type: 'error',
        text: errorMessage
      });
      
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const joinVideoConsultation = () => {
    if (consultation) {
      window.open(`/video-consultation/room/${consultation.roomId}`, '_blank');
    }
  };

  const formatDateTime = (date: string, time: string): string => {
    const dateTime = new Date(`${date}T${time}`);
    return dateTime.toLocaleString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTimeUntilAppointment = (): string => {
    const now = new Date();
    const appointmentDateTime = new Date(`${appointment.date}T${appointment.startTime}`);
    const timeDiff = appointmentDateTime.getTime() - now.getTime();
    
    if (timeDiff < 0) {
      return 'Appointment time has passed';
    }
    
    const hours = Math.floor(timeDiff / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days} day${days > 1 ? 's' : ''} remaining`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m remaining`;
    } else {
      return `${minutes}m remaining`;
    }
  };

  // Don't show video button for in-person appointments
  if (appointment.type !== 'VIDEO_CALL') {
    return null;
  }

  return (
    <VideoButtonContainer>
      {message && (
        <StatusMessage type={message.type}>
          {message.type === 'success' && <CheckCircle size={14} />}
          {message.type === 'error' && <AlertCircle size={14} />}
          {message.type === 'info' && <Video size={14} />}
          {message.text}
        </StatusMessage>
      )}

      {!consultation && canCreateVideoConsultation() && (
        <VideoButton
          variant="primary"
          onClick={createVideoConsultation}
          disabled={loading}
        >
          <Video size={16} />
          {loading ? 'Creating...' : 'Create Video Room'}
        </VideoButton>
      )}

      {consultation && (
        <>
          <ConsultationInfo>
            <InfoRow>
              <Calendar size={12} />
              <span>{formatDateTime(appointment.date, appointment.startTime)}</span>
            </InfoRow>
            <InfoRow>
              <Clock size={12} />
              <span>{getTimeUntilAppointment()}</span>
            </InfoRow>
            <InfoRow>
              <User size={12} />
              <span>Room ID: {consultation.roomId.slice(-8)}</span>
            </InfoRow>
          </ConsultationInfo>

          {canJoinConsultation() ? (
            <VideoButton
              variant="success"
              onClick={joinVideoConsultation}
            >
              <Video size={16} />
              Join Video Call
            </VideoButton>
          ) : (
            <VideoButton
              variant="secondary"
              disabled
            >
              <Clock size={16} />
              Available 15 min before
            </VideoButton>
          )}
        </>
      )}
    </VideoButtonContainer>
  );
};

export default AppointmentVideoButton;
