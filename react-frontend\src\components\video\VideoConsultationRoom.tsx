import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Calendar, 
  Clock, 
  User, 
  Stethoscope, 
  MessageCircle, 
  FileText,
  Star,
  AlertCircle,
  CheckCircle,
  X
} from 'lucide-react';
import VideoCallComponent from './VideoCallComponent';
import { videoCallService, VideoConsultation, ConsultationType } from '../../services/videoCallService';
import { useAuth } from '../../contexts/AuthContext';

const RoomContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

const RoomContent = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 20px;
  height: calc(100vh - 40px);

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }
`;

const VideoSection = styled.div`
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
`;

const SidePanel = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const InfoCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
`;

const CardTitle = styled.h3`
  margin: 0 0 16px 0;
  color: #2d3748;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const AppointmentInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const InfoRow = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  color: #4a5568;
  font-size: 14px;
`;

const StatusBadge = styled.span<{ status: string }>`
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  
  background: ${props => {
    switch (props.status) {
      case 'IN_PROGRESS': return '#48bb78';
      case 'WAITING_FOR_DOCTOR': return '#ed8936';
      case 'WAITING_FOR_PATIENT': return '#4299e1';
      case 'SCHEDULED': return '#9f7aea';
      default: return '#718096';
    }
  }};
  color: white;
`;

const ParticipantCard = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f7fafc;
  border-radius: 8px;
  margin-bottom: 8px;
`;

const Avatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
`;

const ParticipantInfo = styled.div`
  flex: 1;
`;

const ParticipantName = styled.div`
  font-weight: 500;
  color: #2d3748;
`;

const ParticipantRole = styled.div`
  font-size: 12px;
  color: #718096;
  text-transform: uppercase;
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
  
  background: ${props => {
    switch (props.variant) {
      case 'primary': return '#4299e1';
      case 'danger': return '#e53e3e';
      default: return '#edf2f7';
    }
  }};
  
  color: ${props => props.variant === 'secondary' ? '#4a5568' : 'white'};
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const LoadingSpinner = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #4a5568;
`;

const ErrorMessage = styled.div`
  background: #fed7d7;
  color: #c53030;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const SuccessMessage = styled.div`
  background: #c6f6d5;
  color: #2f855a;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

interface VideoConsultationRoomProps {}

const VideoConsultationRoom: React.FC<VideoConsultationRoomProps> = () => {
  const { roomId } = useParams<{ roomId: string }>();
  const navigate = useNavigate();
  const { state } = useAuth();
  
  const [consultation, setConsultation] = useState<VideoConsultation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isCallActive, setIsCallActive] = useState(false);

  useEffect(() => {
    if (roomId) {
      loadConsultation();
    }
  }, [roomId]);

  const loadConsultation = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const consultationData = await videoCallService.getConsultationByRoomId(roomId!);
      setConsultation(consultationData);
      
      // Auto-start consultation if it's scheduled
      if (consultationData.status === 'SCHEDULED') {
        await startConsultation();
      } else if (consultationData.status === 'IN_PROGRESS') {
        setIsCallActive(true);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load consultation');
    } finally {
      setLoading(false);
    }
  };

  const startConsultation = async () => {
    if (!consultation) return;
    
    try {
      setError(null);
      const updatedConsultation = await videoCallService.startConsultation(consultation.id);
      setConsultation(updatedConsultation);
      setIsCallActive(true);
      setSuccess('Consultation started successfully!');
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError(err.message || 'Failed to start consultation');
    }
  };

  const endConsultation = async () => {
    if (!consultation) return;
    
    try {
      setError(null);
      await videoCallService.endConsultation(consultation.id);
      setIsCallActive(false);
      setSuccess('Consultation ended successfully!');
      
      // Redirect after 2 seconds
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    } catch (err: any) {
      setError(err.message || 'Failed to end consultation');
    }
  };

  const handleCallEnd = () => {
    setIsCallActive(false);
    if (state.user?.role === 'DOCTOR') {
      // Only doctors can officially end consultations
      endConsultation();
    } else {
      // Patients just leave the call
      navigate('/dashboard');
    }
  };

  const handleCallError = (errorMessage: string) => {
    setError(errorMessage);
    setIsCallActive(false);
  };

  const getInitials = (name: string): string => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const formatDateTime = (dateTime: string): string => {
    return new Date(dateTime).toLocaleString();
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'IN_PROGRESS': return '#48bb78';
      case 'WAITING_FOR_DOCTOR': return '#ed8936';
      case 'WAITING_FOR_PATIENT': return '#4299e1';
      case 'SCHEDULED': return '#9f7aea';
      default: return '#718096';
    }
  };

  if (loading) {
    return (
      <RoomContainer>
        <LoadingSpinner>
          Loading consultation room...
        </LoadingSpinner>
      </RoomContainer>
    );
  }

  if (!consultation) {
    return (
      <RoomContainer>
        <RoomContent>
          <ErrorMessage>
            <AlertCircle size={20} />
            Consultation not found or you don't have access to this room.
          </ErrorMessage>
        </RoomContent>
      </RoomContainer>
    );
  }

  return (
    <RoomContainer>
      <RoomContent>
        <VideoSection>
          {isCallActive ? (
            <VideoCallComponent
              consultation={consultation}
              onCallEnd={handleCallEnd}
              onError={handleCallError}
            />
          ) : (
            <div style={{ padding: '40px', textAlign: 'center' }}>
              <h2>Video Consultation</h2>
              <p>Click "Join Call" to start the video consultation</p>
              {!isCallActive && consultation.status === 'SCHEDULED' && (
                <ActionButton variant="primary" onClick={startConsultation}>
                  Join Call
                </ActionButton>
              )}
            </div>
          )}
        </VideoSection>

        <SidePanel>
          {error && (
            <ErrorMessage>
              <AlertCircle size={20} />
              {error}
              <button 
                onClick={() => setError(null)}
                style={{ marginLeft: 'auto', background: 'none', border: 'none', cursor: 'pointer' }}
              >
                <X size={16} />
              </button>
            </ErrorMessage>
          )}

          {success && (
            <SuccessMessage>
              <CheckCircle size={20} />
              {success}
            </SuccessMessage>
          )}

          <InfoCard>
            <CardTitle>
              <Calendar size={20} />
              Appointment Details
            </CardTitle>
            <AppointmentInfo>
              <InfoRow>
                <Clock size={16} />
                {formatDateTime(consultation.scheduledStartTime)}
              </InfoRow>
              <InfoRow>
                <FileText size={16} />
                Type: {consultation.type.replace('_', ' ')}
              </InfoRow>
              <InfoRow>
                Status: <StatusBadge status={consultation.status}>{consultation.status.replace('_', ' ')}</StatusBadge>
              </InfoRow>
            </AppointmentInfo>
          </InfoCard>

          <InfoCard>
            <CardTitle>
              <User size={20} />
              Participants
            </CardTitle>
            
            <ParticipantCard>
              <Avatar>{getInitials(consultation.doctor.fullName)}</Avatar>
              <ParticipantInfo>
                <ParticipantName>{consultation.doctor.fullName}</ParticipantName>
                <ParticipantRole>Doctor • {consultation.doctor.specialization}</ParticipantRole>
              </ParticipantInfo>
              <Stethoscope size={16} color="#4299e1" />
            </ParticipantCard>

            <ParticipantCard>
              <Avatar>{getInitials(consultation.patient.fullName)}</Avatar>
              <ParticipantInfo>
                <ParticipantName>{consultation.patient.fullName}</ParticipantName>
                <ParticipantRole>Patient</ParticipantRole>
              </ParticipantInfo>
              <User size={16} color="#48bb78" />
            </ParticipantCard>
          </InfoCard>

          <InfoCard>
            <CardTitle>
              <MessageCircle size={20} />
              Actions
            </CardTitle>
            
            {!isCallActive && consultation.status === 'SCHEDULED' && (
              <ActionButton variant="primary" onClick={startConsultation}>
                Join Video Call
              </ActionButton>
            )}
            
            {isCallActive && state.user?.role === 'DOCTOR' && (
              <ActionButton variant="danger" onClick={endConsultation}>
                End Consultation
              </ActionButton>
            )}
            
            <ActionButton variant="secondary" onClick={() => navigate('/dashboard')}>
              Back to Dashboard
            </ActionButton>
          </InfoCard>
        </SidePanel>
      </RoomContent>
    </RoomContainer>
  );
};

export default VideoConsultationRoom;
